import sys
sys.path.append("/home/<USER>/usd_mm/")
from byex.spot.market import Spot
from byex.spot.trade import SpotTrade
import random
import time
import pandas as pd
import numpy as np
import config
import con_pri
import logging
import requests
import pytz
from datetime import datetime


beijing_tz = pytz.timezone('Asia/Shanghai')

spot_market = Spot(con_pri.api_key, con_pri.api_secret)
spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)

# 记录上次检查交易的时间
last_trade_check_time = time.time()


def send_telegram_message(text):
    bot_token = con_pri.bot_token
    chat_id = con_pri.chat_id

    message_parts = []
    message_parts.extend([
        f"{text}",
        # f"information2: {variable 2}"
    ])

    message = "\n".join(message_parts)

    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
    params = {"chat_id": chat_id, "text": message}

    try:
        response = requests.post(url, params=params)
        response.raise_for_status()
    except Exception as e:
        logging.error(f"Failed to send Telegram message: {e} {msg}")


# 自定义时间格式器，确保日志时间是北京时区时间
class BeijingTimeFormatter(logging.Formatter):
    def converter(self, timestamp):
        # 设置时区为北京时间
        dt = datetime.fromtimestamp(timestamp, beijing_tz)
        return dt.timetuple()

logging.basicConfig(
    filename='wash_trading.log',  # Log to this file
    level=logging.DEBUG,  # Set log level to DEBUG to capture all log messages
    format='%(asctime)s - %(levelname)s - %(message)s',  # Log format
    datefmt='%Y-%m-%d %H:%M:%S'  # 指定时间格式
)

# 自定义 Formatter 并应用
logger = logging.getLogger()
for handler in logger.handlers:
    handler.setFormatter(BeijingTimeFormatter('%(asctime)s - %(levelname)s - %(message)s'))


def get_remaining_amount_today(DAILY_LIMIT):
    now = datetime.now()
    elapsed_seconds = now.hour * 3600 + now.minute * 60 + now.second
    total_seconds = 24 * 3600
    used_ratio = elapsed_seconds / total_seconds
    remaining_amount = DAILY_LIMIT * (1 - used_ratio)
    return remaining_amount


def generate_random_qty(decimal_weight: float=0.7, int_weight: float=0.3):
    """
    可调整生成整数和小数的概率的随机交易量生成
    Args:
        decimal_weight: 小数概率
        int_weight: 整数概率

    """
    qty_upper = 5000
    qty_lower = 500
    if random.choices([True, False], weights=[decimal_weight, int_weight])[0] and qty_lower > 10:
        # 概率整数
        if random.choices([True, False], weights=[0.7, 0.3])[0]:
            # 100的倍数整数
            upper_100 = int(qty_upper // 100) * 100
            lower_100 = int(qty_lower // 100) * 100
            random_qty = random.choice(range(lower_100, upper_100 + 1, 100))
        else:
            random_qty = random.randint(int(qty_lower), int(qty_upper))
        return random_qty
    else:
        # 概率小数
        # 几位小数随机
        random_decimal = random.randint(1, 4)
        return round(random.uniform(qty_lower, qty_upper), random_decimal)


def generate_random_side(buy_weight: float=0.5, sell_weight: float=0.5):
    """
    可调整买卖概率的随机交易方向生成
    Args:
        buy_weight: 买的概率
        sell_weight: 卖的概率

    """
    if random.choices([True, False], weights=[buy_weight, sell_weight])[0]:
        return "buy"
    else:
        return "sell"


def generate_random_sleep(random_lower: float=0.5, random_upper: float=3):
    """
    在范围内随机生成睡眠时间，控制下单频率
    Args:
        random_lower: 最小睡眠时间
        random_upper: 最大睡眠时间

    Returns:

    """
    return round(random.uniform(random_lower, random_upper), 1)


def tick_rule_classification(data: pd.DataFrame):
    """
    通过tick rule 对交易进行分类
    Args:
        data: 成交纪录dataframe

    """
    last_price = data['price'].shift(-1)
    data.loc[:, 'tick_rule_type'] = data['price'] - last_price
    data['tick_rule_type'] = data['tick_rule_type'].apply(lambda x: 'buy' if x > 0 else (np.nan if x == 0 else 'sell'))
    data['tick_rule_type'] = data['tick_rule_type'].bfill()
    return data


def main():
    global amount_reset
    df_params = config.get_mm_params()
    df_params = df_params.loc[(df_params['pair1'] == 'USDT') & (df_params['pair2'] == 'USD'), :]
    daily_limit = df_params['daily_limit'].iloc[0]  # 每日刷量总额度限制
    remaining_amount_today = get_remaining_amount_today(daily_limit)
    while True:
        df_params = config.get_mm_params()
        df_params = df_params.loc[(df_params['pair1'] == 'USDT') & (df_params['pair2'] == 'USD'), :]
        p_buy = df_params['bid_price'].iloc[0]
        p_sell = df_params['ask_price'].iloc[0]
        new_daily_limit = df_params['daily_limit'].iloc[0]
        if new_daily_limit != daily_limit:
            logging.info(f"今日刷量额度发生变化，原额度：{daily_limit}, 新额度：{new_daily_limit}")
            daily_limit = new_daily_limit
            remaining_amount_today = get_remaining_amount_today(daily_limit)
        now = datetime.now(beijing_tz)
        if now.hour == 0 and now.minute < 3 and not amount_reset:
            remaining_amount_today = get_remaining_amount_today(daily_limit)
            amount_reset = True
            logging.info("今日刷量额度已重置。")
        elif remaining_amount_today <= 0:
            logging.info("今日刷量额度已达上限，等待次日重置。")
            time.sleep(60 * 3)  # 每3分钟检测一次
            continue
        elif amount_reset and now.hour == 0 and now.minute > 3:
            amount_reset = False
        symbol = 'usdtusd'
        df_trades = pd.DataFrame()
        for _ in range(5):
            try:
                df_asks, df_bids = spot_market.get_orderbook(symbol)
                if df_asks.empty or df_bids.empty:
                    continue
            except Exception as e:
                logging.error(f'获取orderbook失败:{e}')
                if _ == 4:
                    logging.error(f'连续5次获取orderbook失败，请查看')
                    send_telegram_message(f'连续5次获取orderbook失败，请查看')
                    time.sleep(60)
                    continue
                else:
                    time.sleep(1)
                    continue
            # tick rule分类占比统计
            try:
                df_trades = spot_market.get_trades(symbol=symbol)
                if df_trades.empty:
                    continue
                break
            except ConnectionResetError as e:
                logging.error(f'获取成交纪录失败:{e}')
                if _ == 4:
                    logging.error(f'连续5次获取成交纪录失败，请查看')
                    send_telegram_message(f'连续5次获取成交纪录失败，请查看')
                    time.sleep(60)
                    continue
                else:
                    time.sleep(1)
                    continue
        if len(df_trades) < 20:
            buy_rate = 0.5
            sell_rate = 0.5
        else:
            df_trades = tick_rule_classification(df_trades)
            # print(df_trades)
            df_count = df_trades[['tick_rule_type', 'amount']].groupby('tick_rule_type').sum()
            df_count.loc[:, 'count_percentage'] = df_count['amount'] / df_count['amount'].sum()
            try:
                buy_rate = max(round(df_count.loc['buy', 'count_percentage'], 2), 0.35)
            except Exception as e:
                buy_rate = 0.35
            try:
                sell_rate = max(round(df_count.loc['sell', 'count_percentage'], 2), 0.35)
            except Exception as e:
                sell_rate = 0.35
        logging.info(f"买单概率：{buy_rate}, 卖单概率：{sell_rate}")
        order_side = generate_random_side(buy_weight=buy_rate, sell_weight=sell_rate)
        if order_side == "buy":
            base_qty = df_asks['asks_qty'].iloc[0]
            order_qty = min(generate_random_qty(), base_qty*0.9)
            if order_qty > remaining_amount_today:
                order_qty = remaining_amount_today
            if df_asks['asks_price'].iloc[0] == p_sell:
                order_price = df_asks['asks_price'].iloc[0]
                remaining_amount_today -= order_qty
            else:
                logging.info("ask一档为用户铺单，不进行刷单")
                time.sleep(60)
                continue
        else:
            base_qty = df_bids['bids_qty'].iloc[0]
            order_qty = min(generate_random_qty(), base_qty*0.9)
            if order_qty > remaining_amount_today:
                order_qty = remaining_amount_today
            if df_bids['bids_price'].iloc[0] == p_buy:
                order_price = df_bids['bids_price'].iloc[0]
                remaining_amount_today -= order_qty
            else:
                logging.info("bid一档为用户铺单，不进行刷单")
                time.sleep(60)
                continue
        # client_order_id = f"wt_{int(time.time()*1000)}"
        order = spot_client.self_trade(symbol='usdtusd', side=order_side, volume=order_qty, price=str(order_price), type=1) #, clientOrderId=client_order_id)
        logging.info(f'order：{order}, 剩余待刷量：{remaining_amount_today}')
        sleep_time = generate_random_sleep(random_lower=30, random_upper=180)
        logging.info(
            f'交易方向：{order_side}，盘口量：{base_qty}，订单量：{order_qty}，订单价格{order_price}，下次订单时间{sleep_time}s')
        time.sleep(sleep_time)


if __name__ == '__main__':
    main()