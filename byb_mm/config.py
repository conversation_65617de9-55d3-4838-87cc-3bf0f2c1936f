import pandas as pd
import pymysql

# 数据库连接信息
host = "127.0.0.1"  # 服务器地址
port = 3306  # MySQL 端口
user = "hao"  # 数据库用户名
password = "12345678"  # 数据库密码
database = "initial_coin_simulation"  # 要连接的数据库


def get_mm_params():
    # 连接数据库
    conn = pymysql.connect(host=host, port=port, user=user, password=password, database=database)
    try:
        with conn.cursor() as cursor:
            # 执行 SQL 查询
            cursor.execute("SELECT * FROM initial_coin;")
            # 获取查询结果
            result = cursor.fetchall()
            dataframe = pd.DataFrame(result, columns=['pair1', 'pair2', 'num_levels', 'inventory_limit', 'max_inventory',
                                                      'price_precision', 'size_precision', 'base_order_size',
                                                      'total_bid_usdt', 'total_ask_coin_num',
                                                      'risk_aversion', 'front_levels_limit', 'last_update'])
            dataframe[['num_levels', 'inventory_limit', 'max_inventory', 'base_order_size', 'total_bid_usdt', 'total_ask_coin_num', 'risk_aversion', 'front_levels_limit']] = \
                dataframe[['num_levels', 'inventory_limit', 'max_inventory', 'base_order_size', 'total_bid_usdt', 'total_ask_coin_num', 'risk_aversion', 'front_levels_limit']].astype(float)
            return dataframe
    finally:
        conn.close()  # 关闭连接


if __name__ == '__main__':
    mm_params = get_mm_params()
    print(mm_params)
